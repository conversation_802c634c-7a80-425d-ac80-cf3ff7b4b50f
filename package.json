{"name": "ns-shop", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "yarn p:m && next build", "start": "next start", "lint": "npx tsc --noEmit --skipLibCheck --project .", "p:m": "prisma migrate dev", "p:m:r": "prisma migrate reset", "p:s": "prisma studio", "dup": "docker compose up -d", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:e2e": "playwright test", "db:full-setup": "prisma db push --force-reset && npm run db:seed && npm run db:generate-data", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma db push --force-reset && npm run db:seed", "db:generate-data": "tsx scripts/generate-sample-data.ts", "db:validate": "tsx scripts/validate-data.ts"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.10.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.10", "@types/bcryptjs": "^2.4.6", "@types/nprogress": "^0.2.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9.30.0", "framer-motion": "^12.6.2", "jose": "^6.0.10", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.483.0", "next": "^15.3.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-cache": "^5.1.2", "nprogress": "^0.2.0", "radix-ui": "^1.1.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "recharts": "^2.15.3", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.7", "eslint-config-next": "15.2.3", "eslint-config-prettier": "^10.1.1", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "msw": "^2.10.2", "prisma": "6.10.1", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5", "typescript-eslint": "^8.28.0", "whatwg-fetch": "^3.6.20"}, "packageManager": "yarn@4.9.2"}