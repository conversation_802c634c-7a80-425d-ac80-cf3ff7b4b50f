import { TestProduct, TestCategory, AdminUser } from '../helpers/admin-helpers';

/**
 * Test data fixtures cho Admin testing
 */

// Admin users for testing
export const adminUsers: Record<string, AdminUser> = {
  mainAdmin: {
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Main Admin',
    role: 'ADMIN'
  },
  secondaryAdmin: {
    email: '<EMAIL>',
    password: 'admin456',
    name: 'Secondary Admin',
    role: 'ADMIN'
  }
};

// Regular users for testing
export const regularUsers: Record<string, AdminUser> = {
  normalUser: {
    email: '<EMAIL>',
    password: 'user123',
    name: 'Normal User',
    role: 'USER'
  }
};

// Test categories
export const testCategories: Record<string, TestCategory> = {
  fashion: {
    name: 'Thời trang',
    description: 'Danh mục thời trang và phụ kiện',
    image: 'https://example.com/fashion.jpg'
  },
  menFashion: {
    name: 'Thời trang nam',
    description: 'Quần áo và phụ kiện dành cho nam giới',
    image: 'https://example.com/men-fashion.jpg',
    parentId: 'fashion-id' // Will be replaced with actual ID in tests
  },
  womenFashion: {
    name: 'Thời trang nữ',
    description: 'Quần áo và phụ kiện dành cho nữ giới',
    image: 'https://example.com/women-fashion.jpg',
    parentId: 'fashion-id' // Will be replaced with actual ID in tests
  },
  shoes: {
    name: 'Giày dép',
    description: 'Các loại giày dép thời trang',
    image: 'https://example.com/shoes.jpg'
  },
  accessories: {
    name: 'Phụ kiện',
    description: 'Túi xách, đồng hồ, trang sức',
    image: 'https://example.com/accessories.jpg'
  },
  electronics: {
    name: 'Điện tử',
    description: 'Thiết bị điện tử và công nghệ',
    image: 'https://example.com/electronics.jpg'
  }
};

// Test products
export const testProducts: Record<string, TestProduct> = {
  tshirt: {
    name: 'Áo thun nam basic',
    description: 'Áo thun nam chất liệu cotton 100%, form regular fit, phù hợp mặc hàng ngày',
    price: 299000,
    salePrice: 199000,
    sku: 'TSH-001',
    stock: 50,
    categoryId: 'men-fashion-id', // Will be replaced with actual ID in tests
    images: [
      'https://example.com/tshirt-1.jpg',
      'https://example.com/tshirt-2.jpg'
    ],
    featured: true,
    status: 'ACTIVE',
    tags: ['áo thun', 'nam', 'cotton', 'basic']
  },
  dress: {
    name: 'Váy maxi nữ',
    description: 'Váy maxi dài tay, chất liệu voan mềm mại, thiết kế thanh lịch',
    price: 599000,
    sku: 'DRS-001',
    stock: 30,
    categoryId: 'women-fashion-id', // Will be replaced with actual ID in tests
    images: [
      'https://example.com/dress-1.jpg',
      'https://example.com/dress-2.jpg',
      'https://example.com/dress-3.jpg'
    ],
    featured: false,
    status: 'ACTIVE',
    tags: ['váy', 'nữ', 'maxi', 'thanh lịch']
  },
  sneakers: {
    name: 'Giày sneaker thể thao',
    description: 'Giày sneaker thể thao nam nữ, đế cao su chống trượt, thiết kế hiện đại',
    price: 899000,
    salePrice: 699000,
    sku: 'SNK-001',
    stock: 25,
    categoryId: 'shoes-id', // Will be replaced with actual ID in tests
    images: [
      'https://example.com/sneakers-1.jpg',
      'https://example.com/sneakers-2.jpg'
    ],
    featured: true,
    status: 'ACTIVE',
    tags: ['giày', 'sneaker', 'thể thao', 'unisex']
  },
  handbag: {
    name: 'Túi xách nữ da thật',
    description: 'Túi xách nữ chất liệu da thật cao cấp, thiết kế sang trọng, nhiều ngăn tiện dụng',
    price: 1299000,
    sku: 'BAG-001',
    stock: 15,
    categoryId: 'accessories-id', // Will be replaced with actual ID in tests
    images: [
      'https://example.com/handbag-1.jpg',
      'https://example.com/handbag-2.jpg'
    ],
    featured: false,
    status: 'ACTIVE',
    tags: ['túi xách', 'nữ', 'da thật', 'sang trọng']
  },
  outOfStockProduct: {
    name: 'Sản phẩm hết hàng',
    description: 'Sản phẩm test cho trường hợp hết hàng',
    price: 199000,
    sku: 'OOS-001',
    stock: 0,
    categoryId: 'fashion-id',
    images: ['https://example.com/oos-1.jpg'],
    featured: false,
    status: 'OUT_OF_STOCK',
    tags: ['test', 'hết hàng']
  },
  inactiveProduct: {
    name: 'Sản phẩm không hoạt động',
    description: 'Sản phẩm test cho trường hợp không hoạt động',
    price: 299000,
    sku: 'INA-001',
    stock: 10,
    categoryId: 'fashion-id',
    images: ['https://example.com/inactive-1.jpg'],
    featured: false,
    status: 'INACTIVE',
    tags: ['test', 'không hoạt động']
  }
};

// Test data for bulk operations
export const bulkTestProducts: TestProduct[] = [
  {
    name: 'Sản phẩm bulk 1',
    description: 'Sản phẩm test cho bulk operations',
    price: 100000,
    sku: 'BULK-001',
    stock: 10,
    categoryId: 'fashion-id',
    images: ['https://example.com/bulk-1.jpg'],
    featured: false,
    status: 'ACTIVE',
    tags: ['bulk', 'test']
  },
  {
    name: 'Sản phẩm bulk 2',
    description: 'Sản phẩm test cho bulk operations',
    price: 200000,
    sku: 'BULK-002',
    stock: 20,
    categoryId: 'fashion-id',
    images: ['https://example.com/bulk-2.jpg'],
    featured: false,
    status: 'ACTIVE',
    tags: ['bulk', 'test']
  },
  {
    name: 'Sản phẩm bulk 3',
    description: 'Sản phẩm test cho bulk operations',
    price: 300000,
    sku: 'BULK-003',
    stock: 30,
    categoryId: 'fashion-id',
    images: ['https://example.com/bulk-3.jpg'],
    featured: false,
    status: 'ACTIVE',
    tags: ['bulk', 'test']
  }
];

// Invalid test data for validation testing
export const invalidProductData = {
  emptyName: {
    name: '',
    description: 'Sản phẩm với tên rỗng',
    price: 100000,
    sku: 'INVALID-001',
    stock: 10,
    categoryId: 'fashion-id',
    images: [],
    featured: false,
    status: 'ACTIVE',
    tags: []
  },
  negativePrice: {
    name: 'Sản phẩm giá âm',
    description: 'Sản phẩm với giá âm',
    price: -100000,
    sku: 'INVALID-002',
    stock: 10,
    categoryId: 'fashion-id',
    images: [],
    featured: false,
    status: 'ACTIVE',
    tags: []
  },
  duplicateSku: {
    name: 'Sản phẩm SKU trùng',
    description: 'Sản phẩm với SKU đã tồn tại',
    price: 100000,
    sku: 'TSH-001', // Same as tshirt SKU
    stock: 10,
    categoryId: 'fashion-id',
    images: [],
    featured: false,
    status: 'ACTIVE',
    tags: []
  }
};

export const invalidCategoryData = {
  emptyName: {
    name: '',
    description: 'Danh mục với tên rỗng'
  },
  duplicateName: {
    name: 'Thời trang', // Same as existing category
    description: 'Danh mục với tên trùng'
  }
};

// Search test data
export const searchTestData = {
  products: {
    exactMatch: 'Áo thun nam basic',
    partialMatch: 'áo thun',
    noResults: 'sản phẩm không tồn tại xyz123',
    multipleResults: 'thời trang'
  },
  categories: {
    exactMatch: 'Thời trang',
    partialMatch: 'thời',
    noResults: 'danh mục không tồn tại xyz123',
    multipleResults: 'fashion'
  }
};

// Pagination test data
export const paginationTestData = {
  pageSize: 10,
  totalItems: 25, // Will create 25 test products for pagination testing
  expectedPages: 3
};

// Filter test data
export const filterTestData = {
  status: ['ACTIVE', 'INACTIVE', 'OUT_OF_STOCK'],
  featured: [true, false],
  priceRanges: [
    { min: 0, max: 500000 },
    { min: 500000, max: 1000000 },
    { min: 1000000, max: 2000000 }
  ]
};

// Sort test data
export const sortTestData = {
  fields: ['name', 'price', 'stock', 'createdAt'],
  orders: ['asc', 'desc']
};

// Form validation messages (in Vietnamese)
export const validationMessages = {
  product: {
    nameRequired: 'Tên sản phẩm là bắt buộc',
    priceRequired: 'Giá sản phẩm là bắt buộc',
    pricePositive: 'Giá sản phẩm phải lớn hơn 0',
    skuRequired: 'SKU là bắt buộc',
    skuDuplicate: 'SKU đã tồn tại',
    stockRequired: 'Số lượng tồn kho là bắt buộc',
    stockNonNegative: 'Số lượng tồn kho không được âm',
    categoryRequired: 'Danh mục là bắt buộc'
  },
  category: {
    nameRequired: 'Tên danh mục là bắt buộc',
    nameDuplicate: 'Tên danh mục đã tồn tại'
  }
};

// Success messages (in Vietnamese)
export const successMessages = {
  product: {
    created: 'Tạo sản phẩm thành công',
    updated: 'Cập nhật sản phẩm thành công',
    deleted: 'Xóa sản phẩm thành công',
    bulkDeleted: 'Xóa sản phẩm thành công'
  },
  category: {
    created: 'Tạo danh mục thành công',
    updated: 'Cập nhật danh mục thành công',
    deleted: 'Xóa danh mục thành công'
  }
};

// Error messages (in Vietnamese)
export const errorMessages = {
  product: {
    notFound: 'Không tìm thấy sản phẩm',
    createFailed: 'Có lỗi xảy ra khi tạo sản phẩm',
    updateFailed: 'Có lỗi xảy ra khi cập nhật sản phẩm',
    deleteFailed: 'Có lỗi xảy ra khi xóa sản phẩm'
  },
  category: {
    notFound: 'Không tìm thấy danh mục',
    createFailed: 'Có lỗi xảy ra khi tạo danh mục',
    updateFailed: 'Có lỗi xảy ra khi cập nhật danh mục',
    deleteFailed: 'Có lỗi xảy ra khi xóa danh mục',
    hasProducts: 'Không thể xóa danh mục có sản phẩm',
    hasChildren: 'Không thể xóa danh mục có danh mục con'
  },
  general: {
    unauthorized: 'Không có quyền truy cập',
    serverError: 'Lỗi máy chủ nội bộ',
    networkError: 'Lỗi kết nối mạng'
  }
};
