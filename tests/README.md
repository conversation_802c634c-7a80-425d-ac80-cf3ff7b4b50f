# NS Shop Testing Suite

## Tổng Quan

Testing suite cho NS Shop bao gồm các loại test sau:
- **Unit Tests**: Test các functions và components riêng lẻ
- **Integration Tests**: Test tương tác giữa các components
- **E2E Tests**: Test toàn bộ user flows với Playwright

## Cấu Trúc Thư <PERSON>

```
tests/
├── e2e/                          # End-to-End tests với Playwright
│   ├── admin-auth.spec.ts        # Authentication tests
│   ├── admin-products.spec.ts    # Products management tests
│   └── admin-categories.spec.ts  # Categories management tests
├── integration/                  # Integration tests
│   ├── api/                      # API integration tests
│   ├── database.test.ts          # Database tests
│   ├── profile-flow.test.tsx     # Profile update flow tests
│   └── search-flow.test.tsx      # Search functionality tests
├── unit/                         # Unit tests
│   ├── api/                      # API unit tests
│   ├── components/               # Component unit tests
│   ├── helpers/                  # Helper function tests
│   ├── hooks/                    # Custom hooks tests
│   └── lib/                      # Library function tests
├── helpers/                      # Test helper functions
│   ├── admin-helpers.ts          # Admin testing helpers
│   ├── database.ts               # Database helpers
│   ├── profile-helpers.ts        # Profile testing helpers
│   ├── search-helpers.ts         # Search testing helpers
│   └── test-utils.tsx            # General test utilities
├── fixtures/                     # Test data và mock data
│   ├── admin-test-data.ts        # Admin test fixtures
│   └── mock-data.ts              # General mock data
├── mocks/                        # Mock handlers
│   ├── handlers.ts               # MSW request handlers
│   └── server.ts                 # Mock server setup
└── __mocks__/                    # Module mocks
    ├── api-handlers.ts           # API mock handlers
    └── server.ts                 # Server mocks
```

## Cách Chạy Tests

### 1. Cài Đặt Dependencies

```bash
# Cài đặt tất cả dependencies
npm install

# Cài đặt Playwright browsers (chỉ cần chạy 1 lần)
npx playwright install
```

### 2. Chạy Tests

#### Unit Tests
```bash
# Chạy tất cả unit tests
npm run test:unit

# Chạy unit tests với watch mode
npm run test:unit:watch

# Chạy unit tests với coverage
npm run test:unit:coverage
```

#### Integration Tests
```bash
# Chạy tất cả integration tests
npm run test:integration

# Chạy integration tests với watch mode
npm run test:integration:watch
```

#### E2E Tests
```bash
# Khởi động dev server trước (terminal riêng)
npm run dev

# Chạy tất cả E2E tests
npm run test:e2e

# Chạy E2E tests với UI mode
npm run test:e2e:ui

# Chạy E2E tests với debug mode
npm run test:e2e:debug

# Chạy specific test file
npx playwright test tests/e2e/admin-products.spec.ts

# Chạy tests trên specific browser
npx playwright test --project=chromium
```

#### Chạy Tất Cả Tests
```bash
# Chạy tất cả tests (unit + integration + e2e)
npm run test:all
```

### 3. Xem Kết Quả

```bash
# Xem HTML report cho E2E tests
npx playwright show-report

# Xem coverage report cho unit tests
npm run test:unit:coverage
open coverage/lcov-report/index.html
```

## Test Scripts trong package.json

```json
{
  "scripts": {
    "test": "jest",
    "test:unit": "jest --testPathPattern=tests/unit",
    "test:unit:watch": "jest --testPathPattern=tests/unit --watch",
    "test:unit:coverage": "jest --testPathPattern=tests/unit --coverage",
    "test:integration": "jest --testPathPattern=tests/integration",
    "test:integration:watch": "jest --testPathPattern=tests/integration --watch",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:e2e:debug": "playwright test --debug",
    "test:e2e:headed": "playwright test --headed",
    "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e"
  }
}
```

## Test Configuration

### Jest Configuration (jest.config.js)
```javascript
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  testPathIgnorePatterns: ['<rootDir>/tests/e2e/'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{ts,tsx}'
  ]
};
```

### Playwright Configuration (playwright.config.ts)
```typescript
export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3001',
    trace: 'on-first-retry',
  },
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3001',
    reuseExistingServer: !process.env.CI,
  },
});
```

## Test Data Management

### 1. Fixtures
- Sử dụng fixtures cho test data consistency
- Tách biệt test data cho từng test suite
- Mock data realistic và comprehensive

### 2. Database Setup
- Sử dụng test database riêng
- Reset data trước mỗi test suite
- Seed data cần thiết cho tests

### 3. API Mocking
- Sử dụng MSW (Mock Service Worker) cho API mocking
- Mock responses realistic
- Handle error scenarios

## Best Practices

### 1. Test Organization
- Nhóm tests theo feature/module
- Sử dụng describe blocks để tổ chức
- Đặt tên test cases rõ ràng và mô tả

### 2. Test Independence
- Mỗi test phải độc lập
- Không phụ thuộc vào thứ tự chạy
- Clean up sau mỗi test

### 3. Assertions
- Sử dụng meaningful assertions
- Test cả positive và negative cases
- Verify UI state và data state

### 4. Performance
- Tránh unnecessary waits
- Sử dụng parallel execution khi có thể
- Optimize test data setup

## Debugging Tests

### 1. Unit/Integration Tests
```bash
# Debug với VS Code
# Thêm breakpoint và chạy debug configuration

# Debug với console.log
console.log('Debug info:', variable);

# Debug với Jest
npm run test:unit -- --verbose
```

### 2. E2E Tests
```bash
# Debug mode với Playwright
npx playwright test --debug

# Headed mode để xem browser
npx playwright test --headed

# Slow motion
npx playwright test --headed --slowMo=1000

# Trace viewer
npx playwright show-trace trace.zip
```

## Continuous Integration

### GitHub Actions Example
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:unit
      - run: npm run test:integration
      - run: npx playwright install
      - run: npm run test:e2e
```

## Troubleshooting

### Common Issues

1. **Tests timeout**: Tăng timeout hoặc optimize test performance
2. **Flaky tests**: Thêm proper waits và assertions
3. **Database issues**: Check connection và permissions
4. **Browser issues**: Update Playwright browsers

### Getting Help

1. Check test logs và error messages
2. Review test documentation
3. Ask team members
4. Check Playwright/Jest documentation

## Contributing

### Adding New Tests

1. Follow existing patterns và conventions
2. Add appropriate test data fixtures
3. Update documentation nếu cần
4. Ensure tests pass locally trước khi commit

### Test Coverage Goals

- Unit tests: > 80% coverage
- Integration tests: Cover critical user flows
- E2E tests: Cover main user journeys

## Resources

- [Playwright Documentation](https://playwright.dev/)
- [Jest Documentation](https://jestjs.io/)
- [Testing Library](https://testing-library.com/)
- [MSW Documentation](https://mswjs.io/)

---

**Lưu ý**: Luôn chạy tests trước khi commit code và đảm bảo tất cả tests pass trước khi tạo pull request.
