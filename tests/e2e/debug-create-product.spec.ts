import { test, expect } from "@playwright/test";

test.describe("Debug Create Product", () => {
  test("should debug create product form submission", async ({ page }) => {
    const apiResponses: { url: string; status: number; body?: any }[] = [];

    // Track API responses
    page.on("response", async (response) => {
      if (response.url().includes("/api/")) {
        let body;
        try {
          body = await response.json();
        } catch (e) {
          body = await response.text();
        }

        apiResponses.push({
          url: response.url(),
          status: response.status(),
          body,
        });

        console.log(`API Response: ${response.status()} ${response.url()}`);
        if (response.status() >= 400) {
          console.log("Error body:", JSON.stringify(body, null, 2));
        }
      }
    });

    // Track console errors
    const consoleErrors: string[] = [];
    page.on("console", (msg) => {
      if (msg.type() === "error") {
        consoleErrors.push(msg.text());
        console.log("Console error:", msg.text());
      }
    });

    // Login
    await page.goto("/auth/signin");
    await page.waitForLoadState("networkidle");
    await page.fill('input[type="email"]', "<EMAIL>");
    await page.fill('input[type="password"]', "admin123");
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/admin/, { waitUntil: "networkidle" });

    // Navigate to create product page
    await page.goto("/admin/products/create");
    await page.waitForLoadState("networkidle");

    console.log("Create product page URL:", page.url());

    // Wait for page to load
    await page.waitForTimeout(3000);

    // Check if form elements exist - use correct placeholders
    const formElements = [
      'input[placeholder="Nhập tên sản phẩm"]',
      'textarea[placeholder="Nhập mô tả chi tiết sản phẩm"]',
      'input[placeholder="0"]', // price
      'input[placeholder="Mã sản phẩm (tự động tạo nếu để trống)"]',
      "select", // category
      'button[type="submit"]',
    ];

    console.log("\n=== FORM ELEMENTS CHECK ===");
    for (const selector of formElements) {
      const element = page.locator(selector);
      const count = await element.count();
      const visible = count > 0 ? await element.first().isVisible() : false;
      console.log(`${selector}: ${count} elements, visible: ${visible}`);
    }

    // Fill form with minimal required data
    console.log("\n=== FILLING FORM ===");

    try {
      await page.fill(
        'input[placeholder="Nhập tên sản phẩm"]',
        "Test Product Debug"
      );
      console.log("✅ Filled product name");

      await page.fill(
        'textarea[placeholder="Nhập mô tả chi tiết sản phẩm"]',
        "Test description for debugging"
      );
      console.log("✅ Filled description");

      await page.fill('input[placeholder="0"]', "100000");
      console.log("✅ Filled price");

      await page.fill(
        'input[placeholder="Mã sản phẩm (tự động tạo nếu để trống)"]',
        "TEST-DEBUG-001"
      );
      console.log("✅ Filled SKU");

      // Select category
      const categorySelect = page.locator("select").first();
      const options = await categorySelect.locator("option").count();
      console.log(`Found ${options} category options`);

      if (options > 1) {
        await categorySelect.selectOption({ index: 1 }); // Select first non-empty option
        console.log("✅ Selected category");
      } else {
        console.log("❌ No categories available");
      }
    } catch (error) {
      console.log("❌ Error filling form:", error.message);
    }

    // Take screenshot before submit
    await page.screenshot({ path: "debug-create-form-before-submit.png" });

    // Submit form
    console.log("\n=== SUBMITTING FORM ===");

    try {
      await page.click('button[type="submit"]');
      console.log("✅ Clicked submit button");

      // Wait for response or redirect
      await page.waitForTimeout(5000);

      console.log("Current URL after submit:", page.url());
    } catch (error) {
      console.log("❌ Error submitting form:", error.message);
    }

    // Take screenshot after submit
    await page.screenshot({ path: "debug-create-form-after-submit.png" });

    // Check for toast messages
    const toastElements = [
      "[data-sonner-toast]",
      ".toast",
      "text=Tạo sản phẩm thành công",
      "text=Có lỗi xảy ra",
    ];

    console.log("\n=== TOAST MESSAGES ===");
    for (const selector of toastElements) {
      const element = page.locator(selector);
      const count = await element.count();
      if (count > 0) {
        const text = await element.first().textContent();
        console.log(`Found toast: "${text}"`);
      }
    }

    // Summary
    console.log("\n=== SUMMARY ===");
    console.log(`Console errors: ${consoleErrors.length}`);
    consoleErrors.forEach((error) => console.log(`  - ${error}`));

    console.log(`API responses: ${apiResponses.length}`);
    apiResponses.forEach((response) => {
      console.log(`  - ${response.status} ${response.url}`);
      if (response.status >= 400) {
        console.log(`    Error: ${JSON.stringify(response.body)}`);
      }
    });

    console.log(`Final URL: ${page.url()}`);

    // Check if we're still on create page (indicates failure)
    if (page.url().includes("/create")) {
      console.log("❌ Still on create page - form submission failed");
    } else if (page.url().includes("/admin/products")) {
      console.log("✅ Redirected to products list - form submission succeeded");
    } else {
      console.log("⚠️ Unexpected URL after form submission");
    }
  });
});
