import { test, expect } from "@playwright/test";

test.describe("Debug Login", () => {
  test("should debug login process", async ({ page }) => {
    // Clear cookies
    await page.context().clearCookies();

    // Go to signin page
    await page.goto("/auth/signin");

    // Take screenshot of signin page
    await page.screenshot({ path: "debug-signin.png" });

    // Check what elements are available
    console.log("Page title:", await page.title());
    console.log("Current URL:", page.url());

    // Look for form elements
    const emailInput = page.locator('input[type="email"]');
    const passwordInput = page.locator('input[type="password"]');
    const submitButton = page.locator('button[type="submit"]');

    console.log("Email input visible:", await emailInput.isVisible());
    console.log("Password input visible:", await passwordInput.isVisible());
    console.log("Submit button visible:", await submitButton.isVisible());

    // Try to find inputs by placeholder
    const emailByPlaceholder = page.locator('input[placeholder*="email"]');
    const passwordByPlaceholder = page.locator(
      'input[placeholder*="password"]'
    );

    console.log(
      "Email by placeholder visible:",
      await emailByPlaceholder.isVisible()
    );
    console.log(
      "Password by placeholder visible:",
      await passwordByPlaceholder.isVisible()
    );

    // Get all input elements
    const allInputs = page.locator("input");
    const inputCount = await allInputs.count();
    console.log("Total inputs found:", inputCount);

    for (let i = 0; i < inputCount; i++) {
      const input = allInputs.nth(i);
      const type = await input.getAttribute("type");
      const placeholder = await input.getAttribute("placeholder");
      const name = await input.getAttribute("name");
      console.log(
        `Input ${i}: type=${type}, placeholder=${placeholder}, name=${name}`
      );
    }

    // Try to login with admin credentials
    if (
      (await emailByPlaceholder.isVisible()) &&
      (await passwordByPlaceholder.isVisible())
    ) {
      await emailByPlaceholder.fill("<EMAIL>");
      await passwordByPlaceholder.fill("admin123");

      // Take screenshot before submit
      await page.screenshot({ path: "debug-before-submit.png" });

      await submitButton.click();

      // Wait for navigation
      await page.waitForLoadState("networkidle");

      // Take screenshot after submit
      await page.screenshot({ path: "debug-after-submit.png" });

      console.log("URL after login:", page.url());

      // Check if we're redirected to admin
      if (page.url().includes("/admin")) {
        console.log("Login successful!");

        // Check for admin elements
        const h1 = page.locator("h1");
        if (await h1.isVisible()) {
          console.log("H1 text:", await h1.textContent());
        }

        // Look for sidebar
        const sidebar = page.locator('[data-testid="admin-sidebar"]');
        console.log("Admin sidebar visible:", await sidebar.isVisible());

        // Look for any sidebar
        const anySidebar = page.locator('aside, nav, [class*="sidebar"]');
        console.log("Any sidebar found:", await anySidebar.count());
      } else {
        console.log("Login failed - still on signin page");

        // Check for error messages
        const errorMessages = page.locator(
          '[class*="error"], [class*="alert"], .toast'
        );
        const errorCount = await errorMessages.count();
        console.log("Error messages found:", errorCount);

        for (let i = 0; i < errorCount; i++) {
          const error = errorMessages.nth(i);
          if (await error.isVisible()) {
            console.log(`Error ${i}:`, await error.textContent());
          }
        }
      }
    } else {
      console.log("Could not find login form elements");
    }
  });

  test("should check admin page structure", async ({ page }) => {
    // Try to access admin page directly
    await page.goto("/admin");

    console.log("Admin page URL:", page.url());

    // If redirected to signin, try manual login
    if (page.url().includes("/auth/signin")) {
      console.log("Redirected to signin - trying manual login");

      // Find and fill login form
      await page.fill('input[type="email"]', "<EMAIL>");
      await page.fill('input[type="password"]', "admin123");
      await page.click('button[type="submit"]');

      await page.waitForLoadState("networkidle");
      console.log("URL after manual login:", page.url());
    }

    // If we're on admin page, check structure
    if (page.url().includes("/admin")) {
      // Take screenshot
      await page.screenshot({ path: "debug-admin-page.png" });

      // Check page structure
      const body = page.locator("body");
      console.log("Body HTML:", await body.innerHTML());

      // Look for common admin elements
      const commonSelectors = [
        "h1",
        "h2",
        "h3",
        '[data-testid="admin-sidebar"]',
        '[data-testid="admin-header"]',
        "nav",
        "aside",
        '[class*="sidebar"]',
        '[class*="navigation"]',
        "main",
      ];

      for (const selector of commonSelectors) {
        const element = page.locator(selector);
        const count = await element.count();
        if (count > 0) {
          console.log(`${selector}: ${count} found`);
          const first = element.first();
          if (await first.isVisible()) {
            const text = await first.textContent();
            console.log(`  First ${selector} text:`, text?.substring(0, 100));
          }
        }
      }
    }
  });

  test("should check admin products page structure", async ({ page }) => {
    // Login first
    await page.goto("/auth/signin");
    await page.waitForLoadState("networkidle");
    await page.fill('input[type="email"]', "<EMAIL>");
    await page.fill('input[type="password"]', "admin123");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Navigate to products page
    await page.goto("/admin/products");
    await page.waitForLoadState("networkidle");

    console.log("Products page URL:", page.url());

    // Take screenshot
    await page.screenshot({ path: "debug-products-page.png" });

    // Check all h1 elements
    const h1Elements = page.locator("h1");
    const h1Count = await h1Elements.count();
    console.log(`Found ${h1Count} h1 elements:`);

    for (let i = 0; i < h1Count; i++) {
      const h1 = h1Elements.nth(i);
      const text = await h1.textContent();
      const classes = await h1.getAttribute("class");
      console.log(`  H1 ${i}: "${text}" (classes: ${classes})`);
    }

    // Check for main content area
    const mainContent = page.locator("main");
    if (await mainContent.isVisible()) {
      const mainH1 = mainContent.locator("h1");
      const mainH1Count = await mainH1.count();
      console.log(`Main content has ${mainH1Count} h1 elements`);

      if (mainH1Count > 0) {
        const text = await mainH1.first().textContent();
        console.log(`Main h1 text: "${text}"`);
      }
    }

    // Check for create product button/link
    const createButtons = [
      'a[href="/admin/products/create"]',
      'button:has-text("Thêm sản phẩm")',
      'button:has-text("Tạo sản phẩm")',
      'a:has-text("Thêm sản phẩm")',
      'a:has-text("Tạo sản phẩm")',
    ];

    for (const selector of createButtons) {
      const element = page.locator(selector);
      const count = await element.count();
      if (count > 0) {
        console.log(`Found create button: ${selector} (${count} elements)`);
      }
    }

    // Check for products table or list
    const productElements = [
      "table",
      "tbody",
      "tr",
      '[data-testid*="product"]',
      ".product-item",
      ".product-row",
    ];

    for (const selector of productElements) {
      const element = page.locator(selector);
      const count = await element.count();
      if (count > 0) {
        console.log(`Found product elements: ${selector} (${count} elements)`);
      }
    }
  });

  test("should check admin products create page", async ({ page }) => {
    // Login first
    await page.goto("/auth/signin");
    await page.waitForLoadState("networkidle");
    await page.fill('input[type="email"]', "<EMAIL>");
    await page.fill('input[type="password"]', "admin123");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Try to navigate to create page
    await page.goto("/admin/products/create");
    await page.waitForLoadState("networkidle");

    console.log("Create page URL:", page.url());

    // Take screenshot
    await page.screenshot({ path: "debug-create-page.png" });

    // Check if page exists or redirects
    if (page.url().includes("/admin/products/create")) {
      console.log("Create page exists!");

      // Check for form elements
      const formElements = [
        "form",
        'input[type="text"]',
        'input[placeholder*="tên"]',
        'input[placeholder*="Tên"]',
        "textarea",
        'textarea[placeholder*="mô tả"]',
        'textarea[placeholder*="Mô tả"]',
        'input[type="number"]',
        'input[placeholder*="giá"]',
        'input[placeholder*="Giá"]',
        'button[type="submit"]',
      ];

      for (const selector of formElements) {
        const element = page.locator(selector);
        const count = await element.count();
        if (count > 0) {
          console.log(`Found form element: ${selector} (${count} elements)`);

          // Get placeholder for inputs
          if (selector.includes("input") || selector.includes("textarea")) {
            for (let i = 0; i < Math.min(count, 3); i++) {
              const placeholder = await element
                .nth(i)
                .getAttribute("placeholder");
              const name = await element.nth(i).getAttribute("name");
              console.log(
                `  Element ${i}: placeholder="${placeholder}", name="${name}"`
              );
            }
          }
        }
      }
    } else {
      console.log("Create page does not exist or redirected to:", page.url());
    }
  });
});
