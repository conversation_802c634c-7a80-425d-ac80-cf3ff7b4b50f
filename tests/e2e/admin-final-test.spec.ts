import { test, expect } from '@playwright/test';

test.describe('Admin Dashboard - Final Integration Test', () => {
  test('should verify all admin functionality works correctly', async ({ page }) => {
    console.log('🧪 Starting comprehensive admin dashboard test...');
    
    // 1. Test redirect when not authenticated
    console.log('1️⃣ Testing authentication redirect...');
    await page.goto('/admin');
    await expect(page).toHaveURL(/\/auth\/signin/);
    console.log('✅ Unauthenticated users are redirected to signin');
    
    // 2. Test admin login
    console.log('2️⃣ Testing admin login...');
    await page.fill('input[placeholder="<EMAIL>"]', '<EMAIL>');
    await page.fill('input[placeholder="••••••••"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForLoadState('networkidle');
    
    // 3. Test admin dashboard access
    console.log('3️⃣ Testing admin dashboard access...');
    await page.goto('/admin');
    await page.waitForLoadState('networkidle');
    
    // Check if we're on admin dashboard
    const currentUrl = page.url();
    console.log('Current URL:', currentUrl);
    
    if (currentUrl.includes('/admin')) {
      console.log('✅ Successfully accessed admin dashboard');
      
      // 4. Test stats API and dashboard elements
      console.log('4️⃣ Testing dashboard elements...');
      
      // Check for main dashboard title
      const dashboardTitle = page.locator('main h1');
      await expect(dashboardTitle).toBeVisible();
      console.log('✅ Dashboard title is visible');
      
      // Check for stats cards
      const statsCards = page.locator('[data-testid="stats-card"]');
      const statsCount = await statsCards.count();
      console.log(`📊 Found ${statsCount} stats cards`);
      
      if (statsCount > 0) {
        console.log('✅ Stats cards are displayed');
        
        // Check for specific stats
        const revenueStats = page.locator('[data-testid="revenue-stat"]');
        const ordersStats = page.locator('[data-testid="orders-stat"]');
        const usersStats = page.locator('[data-testid="users-stat"]');
        const productsStats = page.locator('[data-testid="products-stat"]');
        
        if (await revenueStats.isVisible()) console.log('✅ Revenue stats visible');
        if (await ordersStats.isVisible()) console.log('✅ Orders stats visible');
        if (await usersStats.isVisible()) console.log('✅ Users stats visible');
        if (await productsStats.isVisible()) console.log('✅ Products stats visible');
      } else {
        console.log('❌ No stats cards found');
      }
      
      // Check for error states
      const errorElement = page.locator('[data-testid="stats-error"]');
      const hasError = await errorElement.isVisible();
      console.log(`Error state: ${hasError ? '❌ Has errors' : '✅ No errors'}`);
      
      // Check for loading states
      const loadingElement = page.locator('[data-testid="stats-loading"]');
      const isLoading = await loadingElement.isVisible();
      console.log(`Loading state: ${isLoading ? '⏳ Still loading' : '✅ Loaded'}`);
      
      // 5. Test admin header
      console.log('5️⃣ Testing admin header...');
      const adminHeader = page.locator('[data-testid="admin-header"]');
      await expect(adminHeader).toBeVisible();
      console.log('✅ Admin header is visible');
      
      const userProfileButton = page.locator('[data-testid="user-profile-button"]');
      if (await userProfileButton.isVisible()) {
        console.log('✅ User profile button is visible');
      }
      
      // 6. Test sidebar
      console.log('6️⃣ Testing admin sidebar...');
      const adminSidebar = page.locator('[data-testid="admin-sidebar"]');
      await expect(adminSidebar).toBeVisible();
      console.log('✅ Admin sidebar is visible');
      
      // 7. Test recent orders section
      console.log('7️⃣ Testing recent orders section...');
      const recentOrders = page.locator('[data-testid="recent-orders"]');
      if (await recentOrders.isVisible()) {
        console.log('✅ Recent orders section is visible');
      }
      
      // 8. Test top products section
      console.log('8️⃣ Testing top products section...');
      const topProducts = page.locator('[data-testid="top-products"]');
      if (await topProducts.isVisible()) {
        console.log('✅ Top products section is visible');
      }
      
    } else {
      console.log('❌ Failed to access admin dashboard');
      console.log('Redirected to:', currentUrl);
    }
    
    console.log('🏁 Admin dashboard test completed');
  });

  test('should test stats API directly', async ({ request, page }) => {
    console.log('🔌 Testing stats API directly...');
    
    // Login first to get session
    await page.goto('/auth/signin');
    await page.fill('input[placeholder="<EMAIL>"]', '<EMAIL>');
    await page.fill('input[placeholder="••••••••"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    
    // Get cookies
    const cookies = await page.context().cookies();
    const cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
    
    // Test API
    const response = await request.get('/api/admin/stats', {
      headers: {
        'Cookie': cookieString
      }
    });
    
    console.log('API Response status:', response.status());
    
    if (response.status() === 200) {
      const data = await response.json();
      console.log('✅ API stats successful');
      console.log('📊 Stats data structure:');
      console.log('- Overview keys:', Object.keys(data.overview || {}));
      console.log('- Recent orders count:', data.recentOrders?.length || 0);
      console.log('- Top products count:', data.topProducts?.length || 0);
      console.log('- Low stock products count:', data.lowStockProducts?.length || 0);
      
      // Verify data structure
      expect(data).toHaveProperty('overview');
      expect(data).toHaveProperty('recentOrders');
      expect(data).toHaveProperty('topProducts');
      expect(data).toHaveProperty('lowStockProducts');
      
      if (data.overview) {
        expect(data.overview).toHaveProperty('totalRevenue');
        expect(data.overview).toHaveProperty('totalOrders');
        expect(data.overview).toHaveProperty('totalUsers');
        expect(data.overview).toHaveProperty('totalProducts');
        console.log('✅ API data structure is correct');
      }
    } else {
      console.log('❌ API stats failed with status:', response.status());
      const text = await response.text();
      console.log('Response:', text);
    }
  });
});
