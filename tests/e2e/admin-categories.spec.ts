import { test, expect } from "@playwright/test";
import {
  loginAsAdmin,
  navigateToAdminCategories,
  createCategoryViaUI,
  deleteCategoryViaUI,
  searchCategories,
  verifyCategoryExists,
  waitForAdminPageLoad,
} from "../helpers/admin-helpers";
import {
  adminUsers,
  testCategories,
  invalidCategoryData,
  searchTestData,
  validationMessages,
  successMessages,
  errorMessages,
} from "../fixtures/admin-test-data";

test.describe("Admin Categories Management", () => {
  test.beforeEach(async ({ page }) => {
    // Clear cookies và đăng nhập admin
    await page.context().clearCookies();
    await loginAsAdmin(page, adminUsers.mainAdmin);
    await waitForAdminPageLoad(page);
  });

  test.describe("Categories List Page", () => {
    test("should display categories list page correctly", async ({ page }) => {
      await navigateToAdminCategories(page);

      // Verify page elements
      await expect(page.locator("main h1")).toContainText("Quản lý danh mục");
      await expect(
        page.locator('a[href="/admin/categories/create"]')
      ).toBeVisible();

      // Verify create button
      await expect(
        page.locator('button:has-text("Thêm danh mục")')
      ).toBeVisible();

      // Verify search functionality
      const searchInput = page.locator('input[placeholder*="Tìm kiếm"]');
      if (await searchInput.isVisible()) {
        await expect(searchInput).toBeVisible();
      }
    });

    test("should show empty state when no categories exist", async ({
      page,
    }) => {
      await navigateToAdminCategories(page);

      // Check for empty state
      const emptyState = page.locator("text=Chưa có danh mục nào");
      if (await emptyState.isVisible()) {
        await expect(emptyState).toBeVisible();
        await expect(
          page.locator("text=Bắt đầu bằng cách tạo danh mục đầu tiên")
        ).toBeVisible();
        await expect(
          page.locator('a[href="/admin/categories/create"]')
        ).toBeVisible();
      }
    });

    test("should display categories in hierarchical structure", async ({
      page,
    }) => {
      // Create parent category first
      await createCategoryViaUI(page, testCategories.fashion);

      // Create child category
      const _childCategory = {
        ...testCategories.menFashion,
        parentId: "fashion-id", // This would be replaced with actual ID in real test
      };

      await navigateToAdminCategories(page);

      // Verify parent category exists
      await verifyCategoryExists(page, testCategories.fashion.name);

      // Verify hierarchical display structure
      const categoryTree = page.locator('[data-testid="category-tree"]');
      if (await categoryTree.isVisible()) {
        await expect(categoryTree).toBeVisible();
      }
    });

    test("should display category statistics", async ({ page }) => {
      await navigateToAdminCategories(page);

      // Check for statistics cards
      const statsCards = page.locator('[data-testid="category-stats"]');
      if (await statsCards.isVisible()) {
        await expect(page.locator("text=Tổng danh mục")).toBeVisible();
        await expect(page.locator("text=Danh mục cha")).toBeVisible();
      }
    });
  });

  test.describe("Create Category", () => {
    test("should create new category successfully", async ({ page }) => {
      await createCategoryViaUI(page, testCategories.fashion);

      // Verify redirect to categories list
      await expect(page).toHaveURL("/admin/categories");

      // Verify success message
      await expect(page.locator(".toast")).toContainText(
        successMessages.category.created
      );

      // Verify category appears in list
      await verifyCategoryExists(page, testCategories.fashion.name);
    });

    test("should validate required fields", async ({ page }) => {
      await page.goto("/admin/categories/create");

      // Try to submit empty form
      await page.click('button[type="submit"]');

      // Verify validation messages
      await expect(
        page.locator("text=" + validationMessages.category.nameRequired)
      ).toBeVisible();
    });

    test("should create child category with parent selection", async ({
      page,
    }) => {
      // Create parent category first
      await createCategoryViaUI(page, testCategories.fashion);

      // Create child category
      await page.goto("/admin/categories/create");

      await page.fill(
        'input[placeholder="Nhập tên danh mục"]',
        testCategories.menFashion.name
      );
      await page.fill(
        'textarea[placeholder="Mô tả danh mục"]',
        testCategories.menFashion.description
      );

      // Select parent category
      const parentSelect = page.locator('select[name="parentId"]');
      if (await parentSelect.isVisible()) {
        await parentSelect.selectOption({ label: testCategories.fashion.name });
      }

      await page.click('button[type="submit"]');

      // Verify success
      await expect(page.locator(".toast")).toContainText(
        successMessages.category.created
      );
      await verifyCategoryExists(page, testCategories.menFashion.name);
    });

    test("should handle image upload for category", async ({ page }) => {
      await page.goto("/admin/categories/create");

      // Fill category info with image
      await page.fill(
        'input[placeholder="Nhập tên danh mục"]',
        testCategories.shoes.name
      );
      await page.fill(
        'textarea[placeholder="Mô tả danh mục"]',
        testCategories.shoes.description
      );

      if (testCategories.shoes.image) {
        await page.fill(
          'input[placeholder="URL hình ảnh"]',
          testCategories.shoes.image
        );
      }

      await page.click('button[type="submit"]');

      // Verify success
      await expect(page.locator(".toast")).toContainText(
        successMessages.category.created
      );
    });

    test("should validate category name uniqueness", async ({ page }) => {
      // Create first category
      await createCategoryViaUI(page, testCategories.accessories);

      // Try to create another category with same name
      await page.goto("/admin/categories/create");

      await page.fill(
        'input[placeholder="Nhập tên danh mục"]',
        testCategories.accessories.name
      );
      await page.fill(
        'textarea[placeholder="Mô tả danh mục"]',
        "Different description"
      );

      await page.click('button[type="submit"]');

      // Verify error message
      await expect(page.locator(".toast")).toContainText(
        validationMessages.category.nameDuplicate
      );
    });
  });

  test.describe("Edit Category", () => {
    test("should edit category successfully", async ({ page }) => {
      // Create category first
      await createCategoryViaUI(page, testCategories.electronics);

      // Navigate to edit page
      await navigateToAdminCategories(page);
      const categoryItem = page.locator(
        `div:has-text("${testCategories.electronics.name}")`
      );
      await categoryItem.locator('button[data-testid="edit-button"]').click();

      // Verify edit page
      await expect(page).toHaveURL(/\/admin\/categories\/.*\/edit/);
      await expect(page.locator("main h1")).toContainText("Chỉnh sửa danh mục");

      // Update category info
      const updatedName = "Điện tử và Công nghệ";
      const updatedDescription = "Thiết bị điện tử, công nghệ và phụ kiện";

      await page.fill(
        'input[value="' + testCategories.electronics.name + '"]',
        updatedName
      );
      await page.fill("textarea", updatedDescription);

      // Submit changes
      await page.click('button:has-text("Lưu thay đổi")');

      // Verify success
      await expect(page.locator(".toast")).toContainText(
        successMessages.category.updated
      );
      await expect(page).toHaveURL("/admin/categories");

      // Verify updated category in list
      await verifyCategoryExists(page, updatedName);
    });

    test("should cancel edit and return to categories list", async ({
      page,
    }) => {
      // Create category first
      await createCategoryViaUI(page, testCategories.fashion);

      // Navigate to edit page
      await navigateToAdminCategories(page);
      const categoryItem = page.locator(
        `div:has-text("${testCategories.fashion.name}")`
      );
      await categoryItem.locator('button[data-testid="edit-button"]').click();

      // Click cancel/back button
      await page.click('button:has-text("Quay lại")');

      // Verify return to categories list
      await expect(page).toHaveURL("/admin/categories");
    });

    test("should update parent category relationship", async ({ page }) => {
      // Create parent categories
      await createCategoryViaUI(page, testCategories.fashion);
      await createCategoryViaUI(page, testCategories.accessories);

      // Create child category
      await createCategoryViaUI(page, testCategories.menFashion);

      // Edit child category to change parent
      await navigateToAdminCategories(page);
      const categoryItem = page.locator(
        `div:has-text("${testCategories.menFashion.name}")`
      );
      await categoryItem.locator('button[data-testid="edit-button"]').click();

      // Change parent category
      const parentSelect = page.locator('select[name="parentId"]');
      if (await parentSelect.isVisible()) {
        await parentSelect.selectOption({
          label: testCategories.accessories.name,
        });
      }

      await page.click('button:has-text("Lưu thay đổi")');

      // Verify success
      await expect(page.locator(".toast")).toContainText(
        successMessages.category.updated
      );
    });
  });

  test.describe("Delete Category", () => {
    test("should delete empty category successfully", async ({ page }) => {
      // Create category without products
      await createCategoryViaUI(page, testCategories.shoes);

      // Delete category
      await deleteCategoryViaUI(page, testCategories.shoes.name);

      // Verify category is removed from list
      const categoryItem = page.locator(
        `div:has-text("${testCategories.shoes.name}")`
      );
      await expect(categoryItem).not.toBeVisible();
    });

    test("should show confirmation dialog before delete", async ({ page }) => {
      // Create category
      await createCategoryViaUI(page, testCategories.accessories);

      // Click delete button
      await navigateToAdminCategories(page);
      const categoryItem = page.locator(
        `div:has-text("${testCategories.accessories.name}")`
      );
      await categoryItem.locator('button[data-testid="delete-button"]').click();

      // Verify confirmation dialog
      await expect(
        page.locator("text=Bạn có chắc chắn muốn xóa")
      ).toBeVisible();
      await expect(page.locator('button:has-text("Xác nhận")')).toBeVisible();
      await expect(page.locator('button:has-text("Hủy")')).toBeVisible();

      // Cancel deletion
      await page.click('button:has-text("Hủy")');

      // Verify category still exists
      await verifyCategoryExists(page, testCategories.accessories.name);
    });

    test("should prevent deletion of category with products", async ({
      page,
    }) => {
      // This test assumes there's a category with products
      // In real scenario, you'd create a category and assign products to it

      await navigateToAdminCategories(page);

      // Look for delete button that should be disabled
      const categoryWithProducts = page.locator(
        '[data-testid="category-with-products"]'
      );
      if (await categoryWithProducts.isVisible()) {
        const deleteButton = categoryWithProducts.locator(
          'button[data-testid="delete-button"]'
        );
        await expect(deleteButton).toBeDisabled();

        // Verify tooltip or message about why deletion is disabled
        await deleteButton.hover();
        await expect(
          page.locator("text=Không thể xóa danh mục có sản phẩm")
        ).toBeVisible();
      }
    });

    test("should prevent deletion of category with children", async ({
      page,
    }) => {
      // Create parent and child categories
      await createCategoryViaUI(page, testCategories.fashion);

      // Create child category (this would need actual parent ID in real test)
      await createCategoryViaUI(page, testCategories.menFashion);

      await navigateToAdminCategories(page);

      // Try to delete parent category
      const parentCategory = page.locator(
        `div:has-text("${testCategories.fashion.name}")`
      );
      const deleteButton = parentCategory.locator(
        'button[data-testid="delete-button"]'
      );

      // Delete button should be disabled for parent with children
      if (await deleteButton.isVisible()) {
        await expect(deleteButton).toBeDisabled();
      }
    });
  });

  test.describe("Search and Filter", () => {
    test("should search categories by name", async ({ page }) => {
      // Create test categories
      await createCategoryViaUI(page, testCategories.fashion);
      await createCategoryViaUI(page, testCategories.electronics);

      await navigateToAdminCategories(page);

      // Search for specific category
      await searchCategories(page, searchTestData.categories.exactMatch);

      // Verify search results
      await verifyCategoryExists(page, testCategories.fashion.name);

      // Verify other category is not visible
      const electronicsItem = page.locator(
        `div:has-text("${testCategories.electronics.name}")`
      );
      await expect(electronicsItem).not.toBeVisible();
    });

    test("should show no results for invalid search", async ({ page }) => {
      await navigateToAdminCategories(page);

      // Search for non-existent category
      await searchCategories(page, searchTestData.categories.noResults);

      // Verify no results message
      await expect(
        page.locator("text=Không tìm thấy danh mục nào")
      ).toBeVisible();
    });

    test("should clear search results", async ({ page }) => {
      // Create test category
      await createCategoryViaUI(page, testCategories.shoes);

      await navigateToAdminCategories(page);

      // Search for category
      await searchCategories(page, testCategories.shoes.name);
      await verifyCategoryExists(page, testCategories.shoes.name);

      // Clear search
      const searchInput = page.locator('input[placeholder*="Tìm kiếm"]');
      if (await searchInput.isVisible()) {
        await searchInput.fill("");
        await searchInput.press("Enter");

        // Verify all categories are shown again
        await verifyCategoryExists(page, testCategories.shoes.name);
      }
    });
  });

  test.describe("Category Hierarchy Management", () => {
    test("should display parent-child relationships correctly", async ({
      page,
    }) => {
      // Create parent category
      await createCategoryViaUI(page, testCategories.fashion);

      // Create child categories
      await createCategoryViaUI(page, testCategories.menFashion);
      await createCategoryViaUI(page, testCategories.womenFashion);

      await navigateToAdminCategories(page);

      // Verify hierarchical display
      const parentCategory = page.locator(
        `div:has-text("${testCategories.fashion.name}")`
      );
      await expect(parentCategory).toBeVisible();

      // Verify child categories are indented or grouped under parent
      const childCategories = page.locator('[data-testid="child-categories"]');
      if (await childCategories.isVisible()) {
        await expect(
          childCategories.locator(`text=${testCategories.menFashion.name}`)
        ).toBeVisible();
        await expect(
          childCategories.locator(`text=${testCategories.womenFashion.name}`)
        ).toBeVisible();
      }
    });

    test("should expand and collapse category tree", async ({ page }) => {
      // Create parent with children
      await createCategoryViaUI(page, testCategories.fashion);
      await createCategoryViaUI(page, testCategories.menFashion);

      await navigateToAdminCategories(page);

      // Look for expand/collapse button
      const expandButton = page.locator('[data-testid="expand-category"]');
      if (await expandButton.isVisible()) {
        // Test collapse
        await expandButton.click();

        // Verify children are hidden
        const childCategory = page.locator(
          `text=${testCategories.menFashion.name}`
        );
        await expect(childCategory).not.toBeVisible();

        // Test expand
        await expandButton.click();

        // Verify children are shown
        await expect(childCategory).toBeVisible();
      }
    });

    test("should show category depth levels correctly", async ({ page }) => {
      // Create multi-level hierarchy
      await createCategoryViaUI(page, testCategories.fashion);
      await createCategoryViaUI(page, testCategories.menFashion);

      // Create sub-sub category (would need actual IDs in real test)
      const _subSubCategory = {
        name: "Áo thun nam",
        description: "Danh mục áo thun dành cho nam",
        parentId: "men-fashion-id",
      };

      await navigateToAdminCategories(page);

      // Verify different indentation levels
      const level1 = page.locator('[data-level="1"]');
      const level2 = page.locator('[data-level="2"]');
      const _level3 = page.locator('[data-level="3"]');

      if (await level1.isVisible()) {
        await expect(level1).toBeVisible();
      }
      if (await level2.isVisible()) {
        await expect(level2).toBeVisible();
      }
    });
  });

  test.describe("Category Statistics and Counts", () => {
    test("should display product count for each category", async ({ page }) => {
      await createCategoryViaUI(page, testCategories.fashion);
      await navigateToAdminCategories(page);

      // Verify product count is displayed
      const categoryItem = page.locator(
        `div:has-text("${testCategories.fashion.name}")`
      );
      const productCount = categoryItem.locator(
        '[data-testid="product-count"]'
      );

      if (await productCount.isVisible()) {
        await expect(productCount).toBeVisible();
        // Should show "0 sản phẩm" for new category
        await expect(productCount).toContainText("0 sản phẩm");
      }
    });

    test("should display children count for parent categories", async ({
      page,
    }) => {
      // Create parent with children
      await createCategoryViaUI(page, testCategories.fashion);
      await createCategoryViaUI(page, testCategories.menFashion);

      await navigateToAdminCategories(page);

      // Verify children count
      const parentCategory = page.locator(
        `div:has-text("${testCategories.fashion.name}")`
      );
      const childrenCount = parentCategory.locator(
        '[data-testid="children-count"]'
      );

      if (await childrenCount.isVisible()) {
        await expect(childrenCount).toContainText("1 danh mục con");
      }
    });

    test("should update statistics when categories change", async ({
      page,
    }) => {
      await navigateToAdminCategories(page);

      // Check initial stats
      const totalCategories = page.locator('[data-testid="total-categories"]');
      const _parentCategories = page.locator(
        '[data-testid="parent-categories"]'
      );

      if (await totalCategories.isVisible()) {
        const initialTotal = await totalCategories.textContent();

        // Create new category
        await createCategoryViaUI(page, testCategories.accessories);

        // Verify stats updated
        await expect(totalCategories).not.toContainText(initialTotal || "");
      }
    });
  });

  test.describe("Category Images", () => {
    test("should display category images", async ({ page }) => {
      await createCategoryViaUI(page, testCategories.shoes);
      await navigateToAdminCategories(page);

      // Verify category image is displayed
      const categoryItem = page.locator(
        `div:has-text("${testCategories.shoes.name}")`
      );
      const categoryImage = categoryItem.locator("img");

      if ((await categoryImage.isVisible()) && testCategories.shoes.image) {
        await expect(categoryImage).toBeVisible();
        await expect(categoryImage).toHaveAttribute(
          "src",
          testCategories.shoes.image
        );
      }
    });

    test("should show placeholder when no image exists", async ({ page }) => {
      // Create category without image
      const categoryWithoutImage = { ...testCategories.electronics };
      delete categoryWithoutImage.image;

      await createCategoryViaUI(page, categoryWithoutImage);
      await navigateToAdminCategories(page);

      // Verify placeholder is shown
      const categoryItem = page.locator(
        `div:has-text("${categoryWithoutImage.name}")`
      );
      const placeholder = categoryItem.locator(
        '[data-testid="image-placeholder"]'
      );

      if (await placeholder.isVisible()) {
        await expect(placeholder).toBeVisible();
      }
    });
  });

  test.describe("Error Handling", () => {
    test("should handle network errors gracefully", async ({ page }) => {
      await navigateToAdminCategories(page);

      // Simulate network failure
      await page.route("**/api/admin/categories", (route) => route.abort());

      // Try to refresh the page
      await page.reload();

      // Verify error message is shown
      await expect(page.locator("text=Lỗi tải dữ liệu")).toBeVisible();
      await expect(page.locator('button:has-text("Thử lại")')).toBeVisible();
    });

    test("should retry failed requests", async ({ page }) => {
      await navigateToAdminCategories(page);

      // Simulate network failure then success
      let requestCount = 0;
      await page.route("**/api/admin/categories", (route) => {
        requestCount++;
        if (requestCount === 1) {
          route.abort();
        } else {
          route.continue();
        }
      });

      // Try to refresh the page
      await page.reload();

      // Click retry button
      await page.click('button:has-text("Thử lại")');

      // Verify page loads successfully
      await expect(page.locator("main h1")).toContainText("Quản lý danh mục");
    });

    test("should handle server errors during category operations", async ({
      page,
    }) => {
      await navigateToAdminCategories(page);

      // Simulate server error for create operation
      await page.route("**/api/admin/categories", (route) => {
        if (route.request().method() === "POST") {
          route.fulfill({
            status: 500,
            contentType: "application/json",
            body: JSON.stringify({ error: "Lỗi máy chủ nội bộ" }),
          });
        } else {
          route.continue();
        }
      });

      // Try to create category
      await page.goto("/admin/categories/create");
      await page.fill(
        'input[placeholder="Nhập tên danh mục"]',
        "Test Category"
      );
      await page.fill(
        'textarea[placeholder="Mô tả danh mục"]',
        "Test Description"
      );
      await page.click('button[type="submit"]');

      // Verify error message
      await expect(page.locator(".toast")).toContainText("Lỗi máy chủ nội bộ");
    });
  });

  test.describe("Responsive Design", () => {
    test("should work correctly on mobile devices", async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });

      await navigateToAdminCategories(page);

      // Verify mobile layout
      await expect(page.locator("main h1")).toBeVisible();

      // Check if mobile menu or responsive elements are working
      const mobileMenu = page.locator('[data-testid="mobile-menu"]');
      if (await mobileMenu.isVisible()) {
        await expect(mobileMenu).toBeVisible();
      }

      // Test category creation on mobile
      await page.click('button:has-text("Thêm danh mục")');
      await expect(page).toHaveURL("/admin/categories/create");
    });

    test("should work correctly on tablet devices", async ({ page }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });

      await navigateToAdminCategories(page);

      // Verify tablet layout
      await expect(page.locator("main h1")).toBeVisible();

      // Test functionality on tablet
      await createCategoryViaUI(page, testCategories.fashion);
      await verifyCategoryExists(page, testCategories.fashion.name);
    });
  });
});
