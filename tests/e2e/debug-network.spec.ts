import { test, expect } from '@playwright/test';

test.describe('Debug Network Requests', () => {
  test('should track all network requests on products page', async ({ page }) => {
    const requests: { url: string; method: string; headers: any }[] = [];
    const responses: { url: string; status: number; body?: any }[] = [];
    
    // Track all requests
    page.on('request', request => {
      requests.push({
        url: request.url(),
        method: request.method(),
        headers: request.headers(),
      });
      console.log(`→ ${request.method()} ${request.url()}`);
    });
    
    // Track all responses
    page.on('response', async response => {
      let body;
      try {
        if (response.url().includes('/api/')) {
          body = await response.text();
        }
      } catch (e) {
        // Ignore body parsing errors
      }
      
      responses.push({
        url: response.url(),
        status: response.status(),
        body,
      });
      
      console.log(`← ${response.status()} ${response.url()}`);
      
      if (response.status() >= 400) {
        console.log(`❌ Error response: ${response.status()} ${response.url()}`);
        if (body) {
          console.log(`   Body: ${body.substring(0, 200)}`);
        }
      }
    });
    
    // Login
    await page.goto('/auth/signin');
    await page.waitForLoadState('networkidle');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/admin/, { waitUntil: 'networkidle' });
    
    console.log('\n=== NAVIGATING TO PRODUCTS PAGE ===');
    
    // Navigate to products page
    await page.goto('/admin/products');
    await page.waitForLoadState('networkidle');
    
    // Wait for any additional async requests
    await page.waitForTimeout(5000);
    
    console.log('\n=== NETWORK SUMMARY ===');
    console.log(`Total requests: ${requests.length}`);
    console.log(`Total responses: ${responses.length}`);
    
    // Filter API requests
    const apiRequests = requests.filter(req => req.url.includes('/api/'));
    const apiResponses = responses.filter(res => res.url.includes('/api/'));
    
    console.log('\n=== API REQUESTS ===');
    apiRequests.forEach(req => {
      console.log(`${req.method} ${req.url}`);
    });
    
    console.log('\n=== API RESPONSES ===');
    apiResponses.forEach(res => {
      console.log(`${res.status} ${res.url}`);
      if (res.status >= 400 && res.body) {
        console.log(`   Error: ${res.body.substring(0, 100)}`);
      }
    });
    
    // Check for specific problematic requests
    const statsRequests = apiRequests.filter(req => req.url.includes('/stats'));
    const statsResponses = apiResponses.filter(res => res.url.includes('/stats'));
    
    if (statsRequests.length > 0) {
      console.log('\n=== STATS REQUESTS FOUND ===');
      statsRequests.forEach(req => {
        console.log(`${req.method} ${req.url}`);
      });
      
      statsResponses.forEach(res => {
        console.log(`Response: ${res.status} ${res.url}`);
        if (res.body) {
          console.log(`Body: ${res.body.substring(0, 200)}`);
        }
      });
    } else {
      console.log('\n✅ No stats requests found');
    }
    
    // Check for failed requests
    const failedResponses = apiResponses.filter(res => res.status >= 400);
    if (failedResponses.length > 0) {
      console.log('\n❌ FAILED API REQUESTS:');
      failedResponses.forEach(res => {
        console.log(`${res.status} ${res.url}`);
        if (res.body) {
          console.log(`   Error: ${res.body.substring(0, 200)}`);
        }
      });
    } else {
      console.log('\n✅ All API requests successful');
    }
  });

  test('should check if stats API is accessible', async ({ page }) => {
    // Login first
    await page.goto('/auth/signin');
    await page.waitForLoadState('networkidle');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/admin/, { waitUntil: 'networkidle' });
    
    // Test stats API directly
    console.log('\n=== TESTING STATS API DIRECTLY ===');
    
    try {
      const response = await page.request.get('/api/admin/stats');
      console.log(`Stats API status: ${response.status()}`);
      
      if (response.ok()) {
        const data = await response.json();
        console.log('Stats API response structure:', {
          hasOverview: !!data.overview,
          overviewKeys: data.overview ? Object.keys(data.overview) : [],
        });
      } else {
        const text = await response.text();
        console.log(`Stats API error: ${text.substring(0, 200)}`);
      }
    } catch (error) {
      console.log(`Stats API request failed: ${error.message}`);
    }
  });
});
