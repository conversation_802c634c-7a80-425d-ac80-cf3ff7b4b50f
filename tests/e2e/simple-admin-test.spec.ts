import { test, expect } from '@playwright/test';

test.describe('Simple Admin Test', () => {
  test('should access admin products page after login', async ({ page }) => {
    // Go to signin page
    await page.goto('/auth/signin');
    await page.waitForLoadState('networkidle');
    
    // Login
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForLoadState('networkidle');
    
    // Verify we're on admin dashboard
    await expect(page).toHaveURL('/admin');
    console.log('✅ Successfully logged in to admin dashboard');
    
    // Now try to navigate to products page
    await page.goto('/admin/products');
    await page.waitForLoadState('networkidle');
    
    console.log('Current URL after products navigation:', page.url());
    
    // Check if we're still on products page or redirected
    if (page.url().includes('/admin/products')) {
      console.log('✅ Successfully accessed admin products page');
      
      // Check for main h1
      const mainH1 = page.locator('main h1');
      if (await mainH1.isVisible()) {
        const h1Text = await mainH1.textContent();
        console.log('Main H1 text:', h1Text);
        await expect(mainH1).toContainText('Quản lý sản phẩm');
      } else {
        console.log('❌ Main H1 not found');
      }
    } else {
      console.log('❌ Redirected away from products page to:', page.url());
    }
  });

  test('should access admin categories page after login', async ({ page }) => {
    // Go to signin page
    await page.goto('/auth/signin');
    await page.waitForLoadState('networkidle');
    
    // Login
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForLoadState('networkidle');
    
    // Verify we're on admin dashboard
    await expect(page).toHaveURL('/admin');
    console.log('✅ Successfully logged in to admin dashboard');
    
    // Now try to navigate to categories page
    await page.goto('/admin/categories');
    await page.waitForLoadState('networkidle');
    
    console.log('Current URL after categories navigation:', page.url());
    
    // Check if we're still on categories page or redirected
    if (page.url().includes('/admin/categories')) {
      console.log('✅ Successfully accessed admin categories page');
      
      // Check for main h1
      const mainH1 = page.locator('main h1');
      if (await mainH1.isVisible()) {
        const h1Text = await mainH1.textContent();
        console.log('Main H1 text:', h1Text);
        await expect(mainH1).toContainText('Quản lý danh mục');
      } else {
        console.log('❌ Main H1 not found');
      }
    } else {
      console.log('❌ Redirected away from categories page to:', page.url());
    }
  });

  test('should navigate using sidebar links', async ({ page }) => {
    // Go to signin page
    await page.goto('/auth/signin');
    await page.waitForLoadState('networkidle');
    
    // Login
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForLoadState('networkidle');
    
    // Verify we're on admin dashboard
    await expect(page).toHaveURL('/admin');
    
    // Click on products link in sidebar
    await page.click('a[href="/admin/products"]');
    await page.waitForLoadState('networkidle');
    
    console.log('URL after clicking products link:', page.url());
    
    if (page.url().includes('/admin/products')) {
      console.log('✅ Products navigation via sidebar works');
    } else {
      console.log('❌ Products navigation via sidebar failed');
    }
    
    // Go back to dashboard
    await page.click('a[href="/admin"]');
    await page.waitForLoadState('networkidle');
    
    // Click on categories link in sidebar
    await page.click('a[href="/admin/categories"]');
    await page.waitForLoadState('networkidle');
    
    console.log('URL after clicking categories link:', page.url());
    
    if (page.url().includes('/admin/categories')) {
      console.log('✅ Categories navigation via sidebar works');
    } else {
      console.log('❌ Categories navigation via sidebar failed');
    }
  });
});
