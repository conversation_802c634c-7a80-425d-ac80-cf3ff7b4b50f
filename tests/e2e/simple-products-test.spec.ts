import { test, expect } from "@playwright/test";

test.describe("Simple Products Test", () => {
  test("should load admin products page without errors", async ({ page }) => {
    // Monitor console errors
    const consoleErrors: string[] = [];
    page.on("console", (msg) => {
      if (msg.type() === "error") {
        consoleErrors.push(msg.text());
      }
    });

    // Monitor page errors
    const pageErrors: string[] = [];
    page.on("pageerror", (error) => {
      pageErrors.push(error.message);
    });

    // Login
    await page.goto("/auth/signin");
    await page.waitForLoadState("networkidle");
    await page.fill('input[type="email"]', "<EMAIL>");
    await page.fill('input[type="password"]', "admin123");
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/admin/, { waitUntil: "networkidle" });

    console.log("✅ Login successful");

    // Navigate to products page
    await page.goto("/admin/products");
    await page.waitForLoadState("networkidle");

    console.log("Current URL:", page.url());

    // Wait a bit for any async operations
    await page.waitForTimeout(3000);

    // Check for errors
    if (consoleErrors.length > 0) {
      console.log("❌ Console errors found:");
      consoleErrors.forEach((error) => console.log("  -", error));
    } else {
      console.log("✅ No console errors");
    }

    if (pageErrors.length > 0) {
      console.log("❌ Page errors found:");
      pageErrors.forEach((error) => console.log("  -", error));
    } else {
      console.log("✅ No page errors");
    }

    // Check if main h1 exists
    const mainH1 = page.locator("main h1");
    if (await mainH1.isVisible()) {
      const h1Text = await mainH1.textContent();
      console.log("✅ Main H1 found:", h1Text);
      await expect(mainH1).toContainText("Quản lý sản phẩm");
    } else {
      console.log("❌ Main H1 not found");

      // Check if there's any h1
      const anyH1 = page.locator("h1");
      const h1Count = await anyH1.count();
      console.log(`Found ${h1Count} h1 elements total`);

      for (let i = 0; i < h1Count; i++) {
        const h1Text = await anyH1.nth(i).textContent();
        console.log(`  H1 ${i}: "${h1Text}"`);
      }
    }

    // Check if page loaded successfully
    const bodyText = await page.locator("body").textContent();
    if (bodyText && bodyText.includes("Quản lý sản phẩm")) {
      console.log("✅ Page contains expected text");
    } else {
      console.log("❌ Page does not contain expected text");
      console.log("Body text length:", bodyText?.length || 0);
    }

    // Check for loading states
    const loadingElements = [
      "text=Loading",
      ".loading",
      ".spinner",
      '[data-loading="true"]',
    ];

    for (const selector of loadingElements) {
      const element = page.locator(selector);
      if (await element.isVisible()) {
        console.log("⏳ Loading element found:", selector);
      }
    }

    // Take screenshot for debugging
    await page.screenshot({ path: "simple-products-test.png" });

    // Basic assertion - page should not have runtime errors
    expect(pageErrors.length).toBe(0);
  });

  test("should have working API endpoint", async ({ page }) => {
    // Login first to get session
    await page.goto("/auth/signin");
    await page.waitForLoadState("networkidle");
    await page.fill('input[type="email"]', "<EMAIL>");
    await page.fill('input[type="password"]', "admin123");
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/admin/, { waitUntil: "networkidle" });

    // Test API endpoint directly
    const response = await page.request.get(
      "/api/admin/products?page=1&limit=20"
    );

    console.log("API Response status:", response.status());
    console.log("API Response headers:", await response.headers());

    if (response.ok()) {
      const data = await response.json();
      console.log("✅ API response successful");
      console.log("Response structure:", {
        success: data.success,
        dataLength: data.data?.length || 0,
        pagination: data.pagination,
      });
    } else {
      console.log("❌ API response failed");
      const text = await response.text();
      console.log("Response text:", text.substring(0, 500));
    }

    expect(response.status()).toBe(200);
  });
});
