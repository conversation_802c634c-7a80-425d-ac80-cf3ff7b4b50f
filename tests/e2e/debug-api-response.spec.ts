import { test, expect } from '@playwright/test';

test.describe('Debug API Response', () => {
  test('should debug products API response', async ({ page }) => {
    const apiResponses: { url: string; status: number; body?: any }[] = [];
    
    // Track API responses
    page.on('response', async response => {
      if (response.url().includes('/api/admin/products')) {
        let body;
        try {
          body = await response.json();
        } catch (e) {
          body = await response.text();
        }
        
        apiResponses.push({
          url: response.url(),
          status: response.status(),
          body,
        });
        
        console.log(`API Response: ${response.status()} ${response.url()}`);
        console.log('Response body:', JSON.stringify(body, null, 2));
      }
    });
    
    // Login
    await page.goto('/auth/signin');
    await page.waitForLoadState('networkidle');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/admin/, { waitUntil: 'networkidle' });
    
    // Navigate to products page
    await page.goto('/admin/products');
    await page.waitForLoadState('networkidle');
    
    // Wait for API calls
    await page.waitForTimeout(5000);
    
    console.log('\n=== API RESPONSES SUMMARY ===');
    console.log(`Total API responses: ${apiResponses.length}`);
    
    apiResponses.forEach((response, index) => {
      console.log(`\nResponse ${index + 1}:`);
      console.log(`  URL: ${response.url}`);
      console.log(`  Status: ${response.status}`);
      
      if (response.body && typeof response.body === 'object') {
        console.log(`  Success: ${response.body.success}`);
        console.log(`  Products count: ${response.body.data?.length || 0}`);
        console.log(`  Pagination: ${JSON.stringify(response.body.pagination || {})}`);
        
        if (response.body.data && response.body.data.length > 0) {
          console.log(`  First product: ${response.body.data[0].name}`);
        }
        
        if (response.body.error) {
          console.log(`  Error: ${response.body.error}`);
        }
      } else {
        console.log(`  Body: ${response.body}`);
      }
    });
    
    // Test API directly
    console.log('\n=== DIRECT API TEST ===');
    try {
      const directResponse = await page.request.get('/api/admin/products?page=1&limit=20');
      console.log(`Direct API status: ${directResponse.status()}`);
      
      if (directResponse.ok()) {
        const data = await directResponse.json();
        console.log('Direct API response:', JSON.stringify(data, null, 2));
      } else {
        const text = await directResponse.text();
        console.log('Direct API error:', text);
      }
    } catch (error) {
      console.log('Direct API request failed:', error.message);
    }
    
    // Check database directly via API
    console.log('\n=== DATABASE CHECK ===');
    try {
      const dbResponse = await page.request.get('/api/admin/products?page=1&limit=100');
      if (dbResponse.ok()) {
        const data = await dbResponse.json();
        console.log(`Database has ${data.data?.length || 0} products`);
        console.log(`Total count: ${data.pagination?.total || 0}`);
      }
    } catch (error) {
      console.log('Database check failed:', error.message);
    }
  });
});
