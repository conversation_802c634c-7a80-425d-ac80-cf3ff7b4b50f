import { test, expect } from "@playwright/test";

test.describe("Admin Stats API", () => {
  test.beforeEach(async ({ page }) => {
    // Đăng nhập với admin trước mỗi test
    await page.goto("/auth/signin");
    await page.fill('input[placeholder="<EMAIL>"]', "<EMAIL>");
    await page.fill('input[placeholder="••••••••"]', "admin123");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");
  });

  test("should load admin dashboard with stats", async ({ page }) => {
    await page.goto("/admin");

    // Kiểm tra trang dashboard load thành công
    await expect(page).toHaveURL("/admin");
    await expect(page.locator("main h1")).toContainText("Dashboard");

    // Kiểm tra stats cards có hiển thị
    const statsCards = page.locator('[data-testid="stats-card"]');
    await expect(statsCards).toHaveCount(4); // Doanh thu, Đơn hàng, Khách hàng, Sản phẩm

    // Kiểm tra loading state biến mất
    await expect(
      page.locator('[data-testid="stats-loading"]')
    ).not.toBeVisible();
  });

  test("should display correct stats data", async ({ page }) => {
    await page.goto("/admin");

    // Đợi stats load xong
    await page.waitForSelector('[data-testid="stats-card"]');

    // Kiểm tra các stats card có data
    const revenueCard = page.locator('[data-testid="revenue-stat"]');
    const ordersCard = page.locator('[data-testid="orders-stat"]');
    const usersCard = page.locator('[data-testid="users-stat"]');
    const productsCard = page.locator('[data-testid="products-stat"]');

    await expect(revenueCard).toBeVisible();
    await expect(ordersCard).toBeVisible();
    await expect(usersCard).toBeVisible();
    await expect(productsCard).toBeVisible();

    // Kiểm tra có hiển thị số liệu (không phải 0 hoặc loading)
    await expect(revenueCard.locator(".stat-value")).not.toHaveText("0");
    await expect(ordersCard.locator(".stat-value")).not.toHaveText("0");
  });

  test("should handle stats API errors gracefully", async ({ page }) => {
    // Intercept API call và trả về error
    await page.route("/api/admin/stats", (route) => {
      route.fulfill({
        status: 500,
        contentType: "application/json",
        body: JSON.stringify({ error: "Internal server error" }),
      });
    });

    await page.goto("/admin");

    // Kiểm tra có hiển thị error state hoặc fallback
    const errorMessage = page.locator('[data-testid="stats-error"]');
    await expect(errorMessage).toBeVisible();
  });

  test("should test stats API endpoint directly", async ({ request }) => {
    // Test API endpoint trực tiếp
    const response = await request.get("/api/admin/stats", {
      headers: {
        // Cần thêm authentication header nếu cần
      },
    });

    expect(response.status()).toBe(200);

    const data = await response.json();

    // Kiểm tra structure của response
    expect(data).toHaveProperty("overview");
    expect(data.overview).toHaveProperty("totalRevenue");
    expect(data.overview).toHaveProperty("totalOrders");
    expect(data.overview).toHaveProperty("totalUsers");
    expect(data.overview).toHaveProperty("totalProducts");

    // Kiểm tra data types
    expect(typeof data.overview.totalRevenue.value).toBe("number");
    expect(typeof data.overview.totalOrders.value).toBe("number");
    expect(typeof data.overview.totalUsers.value).toBe("number");
    expect(typeof data.overview.totalProducts.value).toBe("number");
  });

  test("should test unauthorized access to stats API", async ({ request }) => {
    // Test API without authentication
    const response = await request.get("/api/admin/stats");

    // Should return 401 or 403
    expect([401, 403]).toContain(response.status());
  });

  test("should refresh stats when page is refreshed", async ({ page }) => {
    await page.goto("/admin");

    // Đợi stats load lần đầu
    await page.waitForSelector('[data-testid="stats-card"]');

    // Lấy giá trị stats ban đầu
    const initialRevenue = await page
      .locator('[data-testid="revenue-stat"] .stat-value')
      .textContent();

    // Refresh trang
    await page.reload();

    // Đợi stats load lại
    await page.waitForSelector('[data-testid="stats-card"]');

    // Kiểm tra stats vẫn hiển thị (có thể giống hoặc khác tùy vào data)
    const newRevenue = await page
      .locator('[data-testid="revenue-stat"] .stat-value')
      .textContent();
    expect(newRevenue).toBeTruthy();
  });

  test("should display recent orders section", async ({ page }) => {
    await page.goto("/admin");

    // Kiểm tra section recent orders
    const recentOrdersSection = page.locator('[data-testid="recent-orders"]');
    await expect(recentOrdersSection).toBeVisible();

    // Kiểm tra có hiển thị danh sách orders
    const ordersList = page.locator('[data-testid="orders-list"]');
    await expect(ordersList).toBeVisible();
  });

  test("should display top products section", async ({ page }) => {
    await page.goto("/admin");

    // Kiểm tra section top products
    const topProductsSection = page.locator('[data-testid="top-products"]');
    await expect(topProductsSection).toBeVisible();

    // Kiểm tra có hiển thị danh sách products
    const productsList = page.locator('[data-testid="products-list"]');
    await expect(productsList).toBeVisible();
  });
});
