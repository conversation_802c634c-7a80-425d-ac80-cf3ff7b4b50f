import { test, expect } from "@playwright/test";

test.describe("Admin Authentication", () => {
  test.beforeEach(async ({ page }) => {
    // Đ<PERSON>m bảo không có session nào trước khi test
    await page.context().clearCookies();
  });

  test("should redirect to signin when accessing admin without authentication", async ({
    page,
  }) => {
    // Truy cập trang admin mà không đăng nhập
    await page.goto("/admin");

    // Kiểm tra có redirect đến trang signin không
    await expect(page).toHaveURL(/\/auth\/signin/);

    // Kiểm tra có thông báo hoặc form đăng nhập
    await expect(page.locator("form")).toBeVisible();
  });

  test("should redirect to home when non-admin user tries to access admin", async ({
    page,
  }) => {
    // Tạo một user thường (không phải admin)
    // <PERSON><PERSON><PERSON> là mock - trong thực tế bạn cần tạo user test
    await page.goto("/auth/signin");

    // Đăng nhập với user thường
    await page.fill('input[placeholder="<EMAIL>"]', "<EMAIL>");
    await page.fill('input[placeholder="••••••••"]', "password123");
    await page.click('button[type="submit"]');

    // Đợi redirect sau khi đăng nhập
    await page.waitForURL("/");

    // Thử truy cập admin
    await page.goto("/admin");

    // Kiểm tra có redirect về home không (vì không phải admin)
    await expect(page).toHaveURL("/");
  });

  test("should allow admin user to access admin dashboard", async ({
    page,
  }) => {
    // Đăng nhập với admin user
    await page.goto("/auth/signin");

    // Điền thông tin admin (cần tạo admin user trong database)
    await page.fill('input[placeholder="<EMAIL>"]', "<EMAIL>");
    await page.fill('input[placeholder="••••••••"]', "admin123");
    await page.click('button[type="submit"]');

    // Đợi redirect sau khi đăng nhập
    await page.waitForLoadState("networkidle");

    // Truy cập admin dashboard
    await page.goto("/admin");

    // Kiểm tra có thể truy cập admin dashboard
    await expect(page).toHaveURL("/admin");

    // Kiểm tra có các element admin dashboard
    await expect(page.locator("main h1")).toContainText("Dashboard");
    await expect(page.locator('[data-testid="admin-sidebar"]')).toBeVisible();
  });

  test("should show user info in admin header when logged in", async ({
    page,
  }) => {
    // Đăng nhập với admin
    await page.goto("/auth/signin");
    await page.fill('input[placeholder="<EMAIL>"]', "<EMAIL>");
    await page.fill('input[placeholder="••••••••"]', "admin123");
    await page.click('button[type="submit"]');

    await page.waitForLoadState("networkidle");
    await page.goto("/admin");

    // Kiểm tra header có hiển thị thông tin user
    const header = page.locator('[data-testid="admin-header"]');
    await expect(header).toBeVisible();

    // Kiểm tra có button user profile
    await expect(
      page.locator('button[data-testid="user-profile-button"]')
    ).toBeVisible();
  });

  test("should handle logout from admin panel", async ({ page }) => {
    // Đăng nhập với admin
    await page.goto("/auth/signin");
    await page.fill('input[placeholder="<EMAIL>"]', "<EMAIL>");
    await page.fill('input[placeholder="••••••••"]', "admin123");
    await page.click('button[type="submit"]');

    await page.waitForLoadState("networkidle");
    await page.goto("/admin");

    // Click vào user profile button để mở dropdown
    await page.click('button[data-testid="user-profile-button"]');

    // Click logout
    await page.click('button[data-testid="logout-button"]');

    // Kiểm tra redirect về signin
    await expect(page).toHaveURL(/\/auth\/signin/);

    // Thử truy cập admin lại - should redirect to signin
    await page.goto("/admin");
    await expect(page).toHaveURL(/\/auth\/signin/);
  });
});
