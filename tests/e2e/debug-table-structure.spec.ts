import { test, expect } from '@playwright/test';

test.describe('Debug Table Structure', () => {
  test('should debug table structure on products page', async ({ page }) => {
    // Login
    await page.goto('/auth/signin');
    await page.waitForLoadState('networkidle');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/admin/, { waitUntil: 'networkidle' });
    
    // Navigate to products page
    await page.goto('/admin/products');
    await page.waitForLoadState('networkidle');
    
    console.log('Products page URL:', page.url());
    
    // Wait for page to fully load
    await page.waitForTimeout(5000);
    
    // Take screenshot
    await page.screenshot({ path: 'debug-table-structure.png' });
    
    // Check if main h1 exists
    const mainH1 = page.locator('main h1');
    if (await mainH1.isVisible()) {
      const h1Text = await mainH1.textContent();
      console.log('✅ Main H1 found:', h1Text);
    } else {
      console.log('❌ Main H1 not found');
    }
    
    // Check for table
    const table = page.locator('table');
    const tableCount = await table.count();
    console.log(`Found ${tableCount} tables`);
    
    if (tableCount > 0) {
      console.log('✅ Table found');
      
      // Check table headers
      const headers = page.locator('th');
      const headerCount = await headers.count();
      console.log(`Found ${headerCount} table headers:`);
      
      for (let i = 0; i < headerCount; i++) {
        const headerText = await headers.nth(i).textContent();
        console.log(`  Header ${i}: "${headerText}"`);
      }
      
      // Check table rows
      const rows = page.locator('tbody tr');
      const rowCount = await rows.count();
      console.log(`Found ${rowCount} table rows`);
      
      if (rowCount > 0) {
        console.log('✅ Table has data');
        
        // Check first row content
        const firstRow = rows.first();
        const cells = firstRow.locator('td');
        const cellCount = await cells.count();
        console.log(`First row has ${cellCount} cells:`);
        
        for (let i = 0; i < Math.min(cellCount, 5); i++) {
          const cellText = await cells.nth(i).textContent();
          console.log(`  Cell ${i}: "${cellText?.substring(0, 50)}"`);
        }
      } else {
        console.log('❌ Table has no data rows');
      }
    } else {
      console.log('❌ No table found');
      
      // Check for loading state
      const loadingElements = [
        'text=Loading',
        '.loading',
        '.spinner',
        '[data-loading="true"]',
        '.animate-pulse'
      ];
      
      for (const selector of loadingElements) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          console.log('⏳ Loading element found:', selector);
        }
      }
      
      // Check for empty state
      const emptyStateElements = [
        'text=Không có sản phẩm nào',
        'text=Chưa có sản phẩm',
        'text=No products found',
        '.empty-state'
      ];
      
      for (const selector of emptyStateElements) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          console.log('📭 Empty state found:', selector);
        }
      }
      
      // Check for error messages
      const errorElements = [
        'text=Có lỗi xảy ra',
        'text=Error',
        'text=Failed to fetch',
        '.error'
      ];
      
      for (const selector of errorElements) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          console.log('❌ Error element found:', selector);
        }
      }
    }
    
    // Check all visible text content
    const allText = await page.locator('main').textContent();
    console.log('\n=== PAGE CONTENT ANALYSIS ===');
    console.log('Page content length:', allText?.length || 0);
    
    if (allText) {
      const keywords = ['Sản phẩm', 'SKU', 'Danh mục', 'Giá', 'Kho', 'Trạng thái', 'Thao tác'];
      console.log('Keywords found:');
      keywords.forEach(keyword => {
        if (allText.includes(keyword)) {
          console.log(`  ✅ "${keyword}" found`);
        } else {
          console.log(`  ❌ "${keyword}" not found`);
        }
      });
    }
    
    // Check specific elements that should be present
    const expectedElements = [
      'button:has-text("Thêm sản phẩm")',
      'input[placeholder="Tìm kiếm sản phẩm..."]',
      'button:has-text("Lọc")',
    ];
    
    console.log('\n=== EXPECTED ELEMENTS ===');
    for (const selector of expectedElements) {
      const element = page.locator(selector);
      const count = await element.count();
      if (count > 0) {
        console.log(`✅ Found: ${selector} (${count} elements)`);
      } else {
        console.log(`❌ Not found: ${selector}`);
      }
    }
  });
});
