import { test, expect } from '@playwright/test';

test.describe('Admin Stats Simple Test', () => {
  test('should test admin login and basic dashboard access', async ({ page }) => {
    // Đăng nhập với admin
    await page.goto('/auth/signin');
    
    // Điền thông tin đăng nhập
    await page.fill('input[placeholder="<EMAIL>"]', '<EMAIL>');
    await page.fill('input[placeholder="••••••••"]', 'admin123');
    
    // Click submit
    await page.click('button[type="submit"]');
    
    // Đ<PERSON>i redirect
    await page.waitForLoadState('networkidle');
    
    // Ki<PERSON><PERSON> tra có redirect về home page
    console.log('Current URL after login:', page.url());
    
    // Thử truy cập admin
    await page.goto('/admin');
    
    // Đ<PERSON>i trang load
    await page.waitForLoadState('networkidle');
    
    console.log('Current URL after admin access:', page.url());
    
    // <PERSON><PERSON>m tra có thể truy cập admin dashboard
    if (page.url().includes('/admin')) {
      console.log('✅ Successfully accessed admin dashboard');
      
      // Kiểm tra có stats cards không
      const statsCards = page.locator('[data-testid="stats-card"]');
      const count = await statsCards.count();
      console.log('Stats cards count:', count);
      
      if (count > 0) {
        console.log('✅ Stats cards are visible');
      } else {
        console.log('❌ No stats cards found');
      }
      
      // Kiểm tra có loading state không
      const loadingElement = page.locator('[data-testid="stats-loading"]');
      const isLoading = await loadingElement.isVisible();
      console.log('Is loading:', isLoading);
      
      // Kiểm tra có error state không
      const errorElement = page.locator('[data-testid="stats-error"]');
      const hasError = await errorElement.isVisible();
      console.log('Has error:', hasError);
      
    } else {
      console.log('❌ Failed to access admin dashboard, redirected to:', page.url());
    }
  });

  test('should check API stats endpoint manually', async ({ page, request }) => {
    // Đăng nhập trước
    await page.goto('/auth/signin');
    await page.fill('input[placeholder="<EMAIL>"]', '<EMAIL>');
    await page.fill('input[placeholder="••••••••"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    
    // Lấy cookies từ page context
    const cookies = await page.context().cookies();
    console.log('Cookies:', cookies.map(c => `${c.name}=${c.value}`));
    
    // Thử gọi API stats với cookies
    const response = await request.get('/api/admin/stats', {
      headers: {
        'Cookie': cookies.map(c => `${c.name}=${c.value}`).join('; ')
      }
    });
    
    console.log('API Response status:', response.status());
    
    if (response.status() === 200) {
      const data = await response.json();
      console.log('✅ API stats successful');
      console.log('Stats data keys:', Object.keys(data));
      
      if (data.overview) {
        console.log('Overview data:', data.overview);
      }
    } else {
      console.log('❌ API stats failed');
      const text = await response.text();
      console.log('Response:', text);
    }
  });
});
