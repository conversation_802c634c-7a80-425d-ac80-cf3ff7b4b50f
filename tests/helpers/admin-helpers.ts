import { Page, expect } from "@playwright/test";

/**
 * Helper functions cho Admin testing
 */

export interface AdminUser {
  email: string;
  password: string;
  name: string;
  role: "ADMIN" | "USER";
}

export interface TestProduct {
  name: string;
  description: string;
  price: number;
  salePrice?: number;
  sku: string;
  stock: number;
  categoryId: string;
  images: string[];
  featured: boolean;
  status: "ACTIVE" | "INACTIVE" | "OUT_OF_STOCK";
  tags: string[];
}

export interface TestCategory {
  name: string;
  description: string;
  image?: string;
  parentId?: string;
}

/**
 * Đăng nhập với admin user
 */
export async function loginAsAdmin(
  page: Page,
  adminUser: AdminUser = {
    email: "<EMAIL>",
    password: "admin123",
    name: "Admin User",
    role: "ADMIN",
  }
) {
  await page.goto("/auth/signin");

  // Wait for page to load
  await page.waitForLoadState("networkidle");

  // Điền thông tin đăng nhập
  await page.fill('input[type="email"]', adminUser.email);
  await page.fill('input[type="password"]', adminUser.password);

  // Click đăng nhập
  await page.click('button[type="submit"]');

  // Đợi redirect và load hoàn tất
  await page.waitForLoadState("networkidle");

  // Verify đăng nhập thành công - should be on admin page
  await expect(page).toHaveURL(/\/admin/);
}

/**
 * Đăng xuất khỏi admin
 */
export async function logoutFromAdmin(page: Page) {
  // Click vào user profile button
  await page.click('button[data-testid="user-profile-button"]');

  // Click logout
  await page.click('button[data-testid="logout-button"]');

  // Verify redirect về signin
  await expect(page).toHaveURL(/\/auth\/signin/);
}

/**
 * Navigate đến admin dashboard
 */
export async function navigateToAdminDashboard(page: Page) {
  await page.goto("/admin");
  await expect(page).toHaveURL("/admin");
  await expect(page.locator("main h1")).toContainText("Dashboard");
}

/**
 * Navigate đến admin products page
 */
export async function navigateToAdminProducts(page: Page) {
  await page.goto("/admin/products");
  await expect(page).toHaveURL("/admin/products");
  await expect(page.locator("h1")).toContainText("Quản lý sản phẩm");
}

/**
 * Navigate đến admin categories page
 */
export async function navigateToAdminCategories(page: Page) {
  await page.goto("/admin/categories");
  await expect(page).toHaveURL("/admin/categories");
  await expect(page.locator("h1")).toContainText("Quản lý danh mục");
}

/**
 * Tạo sản phẩm mới qua UI
 */
export async function createProductViaUI(page: Page, product: TestProduct) {
  // Navigate đến trang tạo sản phẩm
  await page.goto("/admin/products/create");
  await expect(page).toHaveURL("/admin/products/create");

  // Điền thông tin sản phẩm
  await page.fill('input[placeholder="Nhập tên sản phẩm"]', product.name);
  await page.fill(
    'textarea[placeholder="Mô tả sản phẩm"]',
    product.description
  );
  await page.fill('input[placeholder="0"]', product.price.toString());

  if (product.salePrice) {
    await page.fill(
      'input[placeholder="Giá khuyến mãi"]',
      product.salePrice.toString()
    );
  }

  await page.fill('input[placeholder="Nhập SKU"]', product.sku);
  await page.fill('input[placeholder="Số lượng"]', product.stock.toString());

  // Chọn danh mục
  await page.selectOption("select", product.categoryId);

  // Thêm hình ảnh
  for (const image of product.images) {
    await page.fill('input[placeholder="URL hình ảnh"]', image);
    await page.click('button:has-text("Thêm hình ảnh")');
  }

  // Thêm tags
  for (const tag of product.tags) {
    await page.fill('input[placeholder="Thêm tag"]', tag);
    await page.press('input[placeholder="Thêm tag"]', "Enter");
  }

  // Set featured
  if (product.featured) {
    await page.check('input[type="checkbox"]');
  }

  // Set status
  await page.selectOption('select[name="status"]', product.status);

  // Submit form
  await page.click('button[type="submit"]');

  // Verify redirect về products list
  await expect(page).toHaveURL("/admin/products");

  // Verify toast success message
  await expect(page.locator(".toast")).toContainText("Tạo sản phẩm thành công");
}

/**
 * Tạo danh mục mới qua UI
 */
export async function createCategoryViaUI(page: Page, category: TestCategory) {
  // Navigate đến trang tạo danh mục
  await page.goto("/admin/categories/create");
  await expect(page).toHaveURL("/admin/categories/create");

  // Điền thông tin danh mục
  await page.fill('input[placeholder="Nhập tên danh mục"]', category.name);
  await page.fill(
    'textarea[placeholder="Mô tả danh mục"]',
    category.description
  );

  if (category.image) {
    await page.fill('input[placeholder="URL hình ảnh"]', category.image);
  }

  if (category.parentId) {
    await page.selectOption('select[name="parentId"]', category.parentId);
  }

  // Submit form
  await page.click('button[type="submit"]');

  // Verify redirect về categories list
  await expect(page).toHaveURL("/admin/categories");

  // Verify toast success message
  await expect(page.locator(".toast")).toContainText("Tạo danh mục thành công");
}

/**
 * Tìm kiếm sản phẩm
 */
export async function searchProducts(page: Page, searchTerm: string) {
  await page.fill('input[placeholder="Tìm kiếm sản phẩm..."]', searchTerm);
  await page.press('input[placeholder="Tìm kiếm sản phẩm..."]', "Enter");
  await page.waitForLoadState("networkidle");
}

/**
 * Tìm kiếm danh mục
 */
export async function searchCategories(page: Page, searchTerm: string) {
  await page.fill('input[placeholder="Tìm kiếm danh mục..."]', searchTerm);
  await page.press('input[placeholder="Tìm kiếm danh mục..."]', "Enter");
  await page.waitForLoadState("networkidle");
}

/**
 * Xóa sản phẩm qua UI
 */
export async function deleteProductViaUI(page: Page, productName: string) {
  // Tìm row chứa sản phẩm
  const productRow = page.locator(`tr:has-text("${productName}")`);
  await expect(productRow).toBeVisible();

  // Click nút xóa
  await productRow.locator('button[data-testid="delete-button"]').click();

  // Confirm deletion
  await page.click('button:has-text("Xác nhận")');

  // Verify toast success message
  await expect(page.locator(".toast")).toContainText("Xóa sản phẩm thành công");

  // Verify sản phẩm không còn trong danh sách
  await expect(productRow).not.toBeVisible();
}

/**
 * Xóa danh mục qua UI
 */
export async function deleteCategoryViaUI(page: Page, categoryName: string) {
  // Tìm category item
  const categoryItem = page.locator(`div:has-text("${categoryName}")`);
  await expect(categoryItem).toBeVisible();

  // Click nút xóa
  await categoryItem.locator('button[data-testid="delete-button"]').click();

  // Confirm deletion
  await page.click('button:has-text("Xác nhận")');

  // Verify toast success message
  await expect(page.locator(".toast")).toContainText("Xóa danh mục thành công");

  // Verify danh mục không còn trong danh sách
  await expect(categoryItem).not.toBeVisible();
}

/**
 * Bulk delete sản phẩm
 */
export async function bulkDeleteProducts(page: Page, productNames: string[]) {
  // Select các sản phẩm
  for (const productName of productNames) {
    const productRow = page.locator(`tr:has-text("${productName}")`);
    await productRow.locator('input[type="checkbox"]').check();
  }

  // Click bulk delete button
  await page.click('button:has-text("Xóa đã chọn")');

  // Confirm deletion
  await page.click('button:has-text("Xác nhận")');

  // Verify toast success message
  await expect(page.locator(".toast")).toContainText("Xóa sản phẩm thành công");
}

/**
 * Verify sản phẩm tồn tại trong danh sách
 */
export async function verifyProductExists(page: Page, productName: string) {
  const productRow = page.locator(`tr:has-text("${productName}")`);
  await expect(productRow).toBeVisible();
}

/**
 * Verify danh mục tồn tại trong danh sách
 */
export async function verifyCategoryExists(page: Page, categoryName: string) {
  const categoryItem = page.locator(`div:has-text("${categoryName}")`);
  await expect(categoryItem).toBeVisible();
}

/**
 * Wait for admin page to load completely
 */
export async function waitForAdminPageLoad(page: Page) {
  await page.waitForLoadState("networkidle");
  await page.waitForSelector('[data-testid="admin-sidebar"]', {
    state: "visible",
  });
}

/**
 * Verify admin sidebar navigation
 */
export async function verifyAdminSidebar(page: Page) {
  const sidebar = page.locator('[data-testid="admin-sidebar"]');
  await expect(sidebar).toBeVisible();

  // Verify main navigation items
  await expect(sidebar.locator('a[href="/admin"]')).toBeVisible();
  await expect(sidebar.locator('a[href="/admin/products"]')).toBeVisible();
  await expect(sidebar.locator('a[href="/admin/categories"]')).toBeVisible();
}
