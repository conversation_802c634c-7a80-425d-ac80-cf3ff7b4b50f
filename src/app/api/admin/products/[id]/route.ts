import { NextRequest } from "next/server";
import { withAdminAuth } from "@/lib/admin/api/middleware";
import { ProductController } from "@/lib/admin/controllers/ProductController";

const productController = new ProductController();

// GET /api/admin/products/[id] - <PERSON><PERSON><PERSON> chi tiết sản phẩm
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return await productController.handleGet(request, { params });
}

// PUT /api/admin/products/[id] - Cậ<PERSON> nhật sản phẩm
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAdminAuth(request, async () => {
    return await productController.handleUpdate(request, { params });
  });
}

// DELETE /api/admin/products/[id] - <PERSON><PERSON><PERSON> sản phẩm
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAdminAuth(request, async () => {
    return await productController.handleDelete(request, { params });
  });
}
