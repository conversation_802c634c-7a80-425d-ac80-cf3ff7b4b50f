import { NextRequest } from "next/server";
import { withAdminAuth } from "@/lib/admin/api/middleware";
import { CategoryController } from "@/lib/admin/controllers/CategoryController";

const categoryController = new CategoryController();

// GET /api/admin/categories - <PERSON><PERSON><PERSON> danh sách categories cho admin
export async function GET(request: NextRequest) {
  return withAdminAuth(request, async () => {
    return await categoryController.handleList(request);
  });
}

// POST /api/admin/categories - Tạo danh mục mới
export async function POST(request: NextRequest) {
  return withAdminAuth(request, async () => {
    return await categoryController.handleCreate(request);
  });
}
